import 'package:flutter/material.dart';
import 'package:nsl/providers/web_home_provider.dart';
import 'package:nsl/utils/screen_constants.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../models/nsl_java_solution_model.dart';
import '../../../services/nsl_java_solution_service.dart';
import '../../../utils/logger.dart';
import '../../../utils/font_manager.dart';
import '../../../utils/responsive_font_sizes.dart';
import 'nsl_java_content_parsing_screen.dart';

class NslJavaSolutionsScreen extends StatefulWidget {
  const NslJavaSolutionsScreen({Key? key}) : super(key: key);

  @override
  State<NslJavaSolutionsScreen> createState() => _NslJavaSolutionsScreenState();
}

class _NslJavaSolutionsScreenState extends State<NslJavaSolutionsScreen> {
  final NslJavaSolutionService _solutionService = NslJavaSolutionService();
  List<NslJavaSolution> _solutions = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadSolutions();
  }

  Future<void> _loadSolutions() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final solutionModel = await _solutionService.getActiveGlobalObjectivesWithFallback();
      
      setState(() {
        _solutions = solutionModel.data ?? [];
        _isLoading = false;
      });

      Logger.info('Loaded ${_solutions.length} NSL Java solutions');
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load solutions: ${e.toString()}';
      });
      Logger.error('Error loading NSL Java solutions: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      child: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              'Error loading solutions',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleMedium(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.red[700],
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                _errorMessage!,
                textAlign: TextAlign.center,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontFamily: FontManager.fontFamilyTiemposText,
                ),
              ),
            ),
          ],
        ),
      );
    }

    if (_solutions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No Solutions Found',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.titleMedium(context),
                fontWeight: FontWeight.w600,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      child: Container(
        padding: const EdgeInsets.only(left: 94, right: 94, bottom: 20, top: 32),
        child: LayoutBuilder(
          builder: (context, constraints) {
            // Calculate available width
            double availableWidth = constraints.maxWidth;
            
            // Calculate items per row (similar to web_solutions_screen.dart)
            const double cardWidth = 136.0;
            double minSpacing = 50.0;
            int itemsPerRow = ((availableWidth + minSpacing) / (cardWidth + minSpacing)).floor();
            if (itemsPerRow == 0) itemsPerRow = 1;
            
            // For large screens, force exactly 8 items per row
            if (availableWidth >= 1520.0) {
              itemsPerRow = 8;
            }

            // Calculate rows needed
            int rowCount = (_solutions.length / itemsPerRow).ceil();

            return Column(
              children: List.generate(rowCount, (rowIndex) {
                // Calculate start and end indices for this row
                int startIndex = rowIndex * itemsPerRow;
                int endIndex = (startIndex + itemsPerRow <= _solutions.length)
                    ? startIndex + itemsPerRow
                    : _solutions.length;

                // Create a list of solutions for this row
                List<NslJavaSolution> rowSolutions = _solutions.sublist(startIndex, endIndex);

                return Padding(
                  padding: const EdgeInsets.only(bottom: 42.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: rowSolutions.asMap().entries.map((entry) {
                      int idx = entry.key;
                      NslJavaSolution solution = entry.value;

                      // Calculate spacing to fit exactly within available width
                      double rightPadding = 0;
                      if (idx < rowSolutions.length - 1) {
                        if (availableWidth >= 1520.0) {
                          rightPadding = (availableWidth - (8 * 136)) / 7;
                        } else {
                          rightPadding = (availableWidth - (itemsPerRow * 136)) / (itemsPerRow - 1);
                        }
                      }

                      return Padding(
                        padding: EdgeInsets.only(right: rightPadding),
                        child: MouseRegion(
                          cursor: SystemMouseCursors.click,
                          child: GestureDetector(
                            onTap: ()async {
                              // Navigate to content parsing screen with the selected goId
                              
                              if (solution.goid != null) {
                                SharedPreferences prefs = await SharedPreferences.getInstance();
                                await prefs.setString('selected_go_id', solution.goid!);
                                 Provider.of<WebHomeProvider>(context, listen: false).currentScreenIndex = ScreenConstants.nslJavaContentParsingScreen;
                                // Navigator.push(
                                //   context,
                                //   MaterialPageRoute(
                                //     builder: (context) => NslJavaContentParsingScreen(
                                //       goId: solution.goid!,
                                //     ),
                                //   ),
                                // );
                              }
                            },
                            child: _buildSolutionCard(solution),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                );
              }),
            );
          },
        ),
      ),
    );
  }

  Widget _buildSolutionCard(NslJavaSolution solution) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Card image placeholder (similar to web_solutions_screen.dart)
        Container(
          width: 136.0,
          height: 200.0,
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Center(
            child: Icon(
              Icons.lightbulb_outline,
              size: 48,
              color: Colors.grey[400],
            ),
          ),
        ),
        const SizedBox(height: 12),
        
        // Solution Name
        SizedBox(
          width: 136.0,
          child: Text(
            solution.name ?? 'Unnamed Solution',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        const SizedBox(height: 2),
        
        // GOID
        SizedBox(
          width: 136.0,
          child: Text(
            solution.goid ?? 'N/A',
            style: FontManager.getCustomStyle(
              fontSize: FontManager.s10,
              fontWeight: FontWeight.w500,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}
