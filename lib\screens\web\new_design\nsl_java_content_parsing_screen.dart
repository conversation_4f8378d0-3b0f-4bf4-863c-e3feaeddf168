import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../models/nsl_hub_java_box_model.dart';
import '../../../models/java_file_model.dart';
import '../../../services/nsl_hub_java_box_grouped_service.dart';
import '../../../utils/logger.dart';
import 'widgets/sidebar.dart';

class NslJavaContentParsingScreen extends StatefulWidget {
  String? goId;

    NslJavaContentParsingScreen({
    Key? key,
    this.goId,
  }) : super(key: key);

  @override
  State<NslJavaContentParsingScreen> createState() =>
      _NslJavaContentParsingScreenState();
}

class _NslJavaContentParsingScreenState
    extends State<NslJavaContentParsingScreen> {
  final NslHubJavaBoxGroupedService _groupedService =
      NslHubJavaBoxGroupedService();

  // Data variables
  List<NslHubJavaBoxData> _allData = [];
  Map<String, List<NslHubJavaBoxData>> _groupedData = {};
  bool _isLoading = true;
  String? _errorMessage;

  // Java file variables
  JavaFileModel? _javaFile;
  List<JavaLine> _javaLines = [];
  bool _isLoadingJavaFile = false;
  String? _javaFileError;

  // Selection variables
  NslHubJavaBoxData? _selectedItem;
  List<int> _selectedLineNumbers = [];
  int _expandedTileIndex = 0; // Default: first tile expanded

  // Theme mode toggle
  bool _isDarkMode = false;

  // Scroll controller for the code panel
  late final ScrollController _codeScrollController;

  // Global key for the code panel
  final GlobalKey _codeViewKey = GlobalKey();

  // Theme constants
  static const Color _darkBackground = Color(0xFF2B2B2B);
  static const Color _darkHeaderColor = Color(0xFF3C3F41);
  static const Color _darkLineNumberBg = Color(0xFF313335);
  static const Color _darkBorderColor = Color(0xFF555555);
  static const Color _lightBackground = Colors.white;
  static const Color _lightHeaderColor = Color(0xFFBBDEFB); // Light blue
  static const Color _lightLineNumberBg = Color(0xFFEEEEEE);
  static const Color _lightBorderColor = Color(0xFFDDDDDD);

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback(
      (_) async {
        SharedPreferences prefs = await SharedPreferences.getInstance();
        String? selectedGoId = prefs.getString('selected_go_id');
        if (selectedGoId != null) {
          widget.goId = selectedGoId;
        }

        _codeScrollController = ScrollController();
        _loadGroupedData();
        _loadJavaFile();
      },
    );
  }

  @override
  void dispose() {
    _codeScrollController.dispose();
    super.dispose();
  }

  Future<void> _loadGroupedData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      Logger.info('Loading grouped data for goId: ${widget.goId}');

      final response = await _groupedService
          .getGroupedDataByGoIdWithFallback(widget.goId ?? '');

      if (response.data != null) {
        final groupedData = _groupedService.groupDataByNslGroup(response.data!);

        setState(() {
          _allData = response.data!;
          _groupedData = groupedData;
          _isLoading = false;
        });

        Logger.info(
            'Loaded ${_allData.length} items in ${_groupedData.length} groups');
      } else {
        setState(() {
          _errorMessage = 'No data received from API';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load data: ${e.toString()}';
      });
      Logger.error('Error loading grouped data: $e');
    }
  }

  Future<void> _loadJavaFile() async {
    try {
      setState(() {
        _isLoadingJavaFile = true;
        _javaFileError = null;
      });

      Logger.info('Loading Java file for goId: ${widget.goId}');

      final javaFileResponse = await _groupedService
          .getJavaFileByGoIdWithFallback(widget.goId ?? '');

      if (javaFileResponse != null) {
        final javaLines = _groupedService.getJavaLines(javaFileResponse);

        setState(() {
          _javaFile = javaFileResponse;
          _javaLines = javaLines;
          _isLoadingJavaFile = false;
        });

        Logger.info('Loaded Java file with ${_javaLines.length} lines');
      } else {
        setState(() {
          _javaFileError = 'No Java file received from API';
          _isLoadingJavaFile = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoadingJavaFile = false;
        _javaFileError = 'Failed to load Java file: ${e.toString()}';
      });
      Logger.error('Error loading Java file: $e');
    }
  }

  void _onItemSelected(NslHubJavaBoxData item) {
    List<int> lineNumbers = _groupedService.parseLineNumbers(item);

    Logger.info('Selected item: ${item.nslName}');
    Logger.info('Start lines: ${item.startLines}');
    Logger.info('End lines: ${item.endLines}');
    Logger.info('Parsed line numbers: $lineNumbers');

    setState(() {
      _selectedItem = item;
      _selectedLineNumbers = lineNumbers;
    });

    // Scroll to the first line if available
    if (lineNumbers.isNotEmpty) {
      _scrollToSelectedLine(lineNumbers.first);
    }
  }

  String _getDynamicIdForSelectedItem() {
    if (_selectedItem == null) {
      return 'N/A';
    }

    // Find the group and item index for the selected item
    int groupIndex = 0;
    for (String groupName in _groupedData.keys) {
      List<NslHubJavaBoxData> items = _groupedData[groupName]!;
      int itemIndex = items.indexOf(_selectedItem!);

      if (itemIndex != -1) {
        // Found the item, calculate dynamic ID
        int groupNumber = groupIndex + 1;
        return '$groupNumber.${itemIndex + 1}';
      }
      groupIndex++;
    }

    return 'N/A';
  }

  // Method to scroll to a specific line in the code panel
  void _scrollToSelectedLine(int lineNumber) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_codeScrollController.hasClients) return;

      final double viewportHeight =
          _codeScrollController.position.viewportDimension;
      final double maxScroll = _codeScrollController.position.maxScrollExtent;

      double targetPosition;

      if (_javaLines.isNotEmpty) {
        // Calculate the percentage position of the target line
        final double linePercentage = (lineNumber - 1) / _javaLines.length;

        // Apply this percentage to the total scrollable content
        targetPosition = linePercentage * maxScroll;

        // Add some offset to center the line in the viewport
        final double centerOffset = viewportHeight * 0.4; // 40% from top
        targetPosition = (targetPosition - centerOffset).clamp(0.0, maxScroll);
      } else {
        // Fallback calculation if code is empty
        const double estimatedLineHeight = 28.0;
        targetPosition = (lineNumber - 1) * estimatedLineHeight;
        targetPosition =
            (targetPosition - viewportHeight * 0.4).clamp(0.0, maxScroll);
      }

      // Animate to the position
      _codeScrollController.animateTo(
        targetPosition,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    });
  }

  // Helper method to get the current theme colors
  Color get editorBackground =>
      _isDarkMode ? _darkBackground : _lightBackground;
  Color get editorHeaderColor =>
      _isDarkMode ? _darkHeaderColor : _lightHeaderColor;
  Color get lineNumberBackground =>
      _isDarkMode ? _darkLineNumberBg : _lightLineNumberBg;
  Color get borderColor => _isDarkMode ? _darkBorderColor : _lightBorderColor;
  Color get textColor => _isDarkMode ? Colors.white : Colors.black;
  Color get lineNumberTextColor =>
      _isDarkMode ? Colors.grey[500]! : Colors.grey[700]!;
  Color get headerIconColor => _isDarkMode ? Colors.white70 : Colors.blue[800]!;
  Color get headerTextColor => _isDarkMode ? Colors.white : Colors.blue[900]!;

  // Toggle theme mode
  void _toggleThemeMode() {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
  }

  // Function to build API data widget
  Widget _buildApiDataWidget() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading NSL Hub Boxing data...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error, color: Colors.red, size: 48),
            SizedBox(height: 16),
            Text(
              'Error loading data',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.red),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadGroupedData,
              child: Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_groupedData.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.info, size: 48, color: Colors.grey),
            SizedBox(height: 16),
            Text('No data available'),
          ],
        ),
      );
    }

    // Convert to ordered list to preserve API order
    List<MapEntry<String, List<NslHubJavaBoxData>>> orderedGroups =
        _groupedData.entries.toList();

    return ListView.builder(
      key: ValueKey('api_data_list_$_expandedTileIndex'),
      itemCount: orderedGroups.length,
      itemBuilder: (context, groupIndex) {
        String groupName = orderedGroups[groupIndex].key;
        List<NslHubJavaBoxData> items = orderedGroups[groupIndex].value;

        // Calculate the group number (1-based)
        int groupNumber = groupIndex + 1;

        return Theme(
          data: Theme.of(context).copyWith(
            dividerColor: Colors.grey[300],
            dividerTheme: const DividerThemeData(
              thickness: 0.5,
              space: 0,
            ),
          ),
          child: ExpansionTile(
            key: ValueKey('expansion_tile_$groupIndex'),
            initiallyExpanded: groupIndex == _expandedTileIndex,
            onExpansionChanged: (bool expanded) {
              setState(() {
                // Always set the expanded tile index when any tile is clicked
                _expandedTileIndex = expanded ? groupIndex : -1;
              });
            },
            title: Text(
              groupName,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            backgroundColor: Colors.grey[50],
            collapsedBackgroundColor: Colors.blue[50],
            textColor: Colors.blue[800],
            children: items.asMap().entries.map((entry) {
              int itemIndex = entry.key;
              NslHubJavaBoxData item = entry.value;

              String displayText = _groupedService.getDisplayText(item);
              bool isSelected = _selectedItem == item;

              // Generate dynamic numbering: groupNumber.itemNumber (e.g., 1.1, 1.2, 2.1, 2.2)
              String dynamicId = '$groupNumber.${itemIndex + 1}';

              return ListTile(
                title: Text(
                  displayText,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                    color: isSelected ? Colors.blue[800] : null,
                  ),
                ),
                dense: true,
                contentPadding: const EdgeInsets.only(left: 28.0, right: 16.0),
                leading: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? Colors.blue.withAlpha(100)
                        : Colors.blue.withAlpha(51),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: isSelected ? Colors.blue[700]! : Colors.blue,
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Text(
                    dynamicId,
                    style: TextStyle(
                      color: isSelected ? Colors.blue[800] : Colors.blue,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  ),
                ),
                selected: isSelected,
                selectedTileColor: Colors.blue.withAlpha(20),
                onTap: () => _onItemSelected(item),
              );
            }).toList(),
          ),
        );
      },
    );
  }

  // Function to build code lines with Java syntax highlighting
  List<Widget> _buildCodeLines() {
    if (_javaLines.isEmpty) return [];

    return _javaLines.map((javaLine) {
      // Get the code text with line number
      String lineText = javaLine.content ?? '';
      String lineNumber = (javaLine.lineNo ?? 0).toString().padLeft(3);
      int currentLineNumber = javaLine.lineNo ?? 0;

      // Check if this line should be highlighted from left panel selection
      bool isHighlightedFromLeftPanel =
          _selectedLineNumbers.contains(currentLineNumber);

      // Apply Java syntax highlighting
      Widget codeText = RichText(
        text: TextSpan(
          style: TextStyle(
            fontFamily: 'monospace',
            fontSize: 14,
            color: textColor,
          ),
          children: _applySyntaxHighlighting(lineText),
        ),
        softWrap: false,
        overflow: TextOverflow.visible,
      );

      // Create the line number widget - NO highlighting for line numbers
      Widget lineNumberWidget = Container(
        width: 50,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        decoration: BoxDecoration(
          color: lineNumberBackground, // Always use normal background
          border: Border(
            right: BorderSide(
              color: borderColor, // Always use normal border
              width: 1,
            ),
          ),
        ),
        child: Text(
          lineNumber,
          style: TextStyle(
            fontFamily: 'monospace',
            fontSize: 14,
            color: lineNumberTextColor, // Always use normal text color
          ),
          textAlign: TextAlign.right,
        ),
      );

      // If this line is highlighted from left panel, wrap ONLY the code content with highlighting
      Widget codeContentWidget = isHighlightedFromLeftPanel
          ? Padding(
              padding: const EdgeInsets.symmetric(vertical: 1.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Code content with highlight
                  Stack(
                    clipBehavior: Clip.none,
                    children: [
                      ClipRect(
                        child: Container(
                          decoration: BoxDecoration(
                            color:
                                Colors.orange.withAlpha(77), // 0.3 * 255 = 77
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 2),
                            child: codeText,
                          ),
                        ),
                      ),
                      // ID Badge at the top right corner showing dynamicId
                      if (_selectedItem != null)
                        Positioned(
                          top: -20,
                          right: 0,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.orange.withAlpha(
                                  200), // Higher opacity for better visibility
                              borderRadius: BorderRadius.circular(4),
                              border:
                                  Border.all(color: Colors.orange, width: 1),
                            ),
                            child: Text(
                              _getDynamicIdForSelectedItem(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            )
          : Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
              child: codeText,
            );

      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 1.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize:
              MainAxisSize.min, // Prevent overflow by using minimum size
          children: [
            // Line number column
            lineNumberWidget,
            // Code content - highlighted if selected from left panel
            codeContentWidget,
          ],
        ),
      );
    }).toList();
  }

  // Helper method to apply basic Java syntax highlighting
  List<TextSpan> _applySyntaxHighlighting(String code) {
    // Define basic Java keywords
    final List<String> keywords = [
      'public',
      'private',
      'protected',
      'static',
      'final',
      'abstract',
      'class',
      'interface',
      'enum',
      'extends',
      'implements',
      'int',
      'boolean',
      'void',
      'String',
      'Integer',
      'Boolean',
      'if',
      'else',
      'for',
      'while',
      'break',
      'continue',
      'return',
      'try',
      'catch',
      'throw',
      'throws',
      'new',
      'this',
      'super',
      'package',
      'import',
      'null',
      'true',
      'false'
    ];

    final RegExp keywordPattern = RegExp(
      r'\b(' + keywords.join('|') + r')\b',
      caseSensitive: true,
    );

    final RegExp stringPattern = RegExp(r'"[^"]*"');
    final RegExp commentPattern = RegExp(r'//.*$');

    // Colors for different syntax elements
    const Color keywordColor = Color(0xFFCC7832); // Orange
    const Color stringColor = Color(0xFF6A8759); // Green
    const Color commentColor = Color(0xFF808080); // Grey

    // Split the code into parts to apply different styles
    List<TextSpan> spans = [];

    // Check for comments first
    if (commentPattern.hasMatch(code)) {
      int lastIndex = 0;
      for (Match match in commentPattern.allMatches(code)) {
        // Add text before the comment
        if (match.start > lastIndex) {
          String beforeText = code.substring(lastIndex, match.start);
          spans.addAll(_processCodeSegment(beforeText, keywordPattern,
              stringPattern, keywordColor, stringColor));
        }
        // Add the highlighted comment
        spans.add(TextSpan(
          text: match.group(0),
          style: TextStyle(color: commentColor, fontStyle: FontStyle.italic),
        ));
        lastIndex = match.end;
      }
      // Add any remaining text
      if (lastIndex < code.length) {
        String afterText = code.substring(lastIndex);
        spans.addAll(_processCodeSegment(afterText, keywordPattern,
            stringPattern, keywordColor, stringColor));
      }
    } else {
      spans.addAll(_processCodeSegment(
          code, keywordPattern, stringPattern, keywordColor, stringColor));
    }

    return spans;
  }

  // Helper method to process a segment of code for regular syntax highlighting
  List<TextSpan> _processCodeSegment(String code, RegExp keywordPattern,
      RegExp stringPattern, Color keywordColor, Color stringColor) {
    List<TextSpan> spans = [];

    // Check for keywords
    if (keywordPattern.hasMatch(code)) {
      int lastIndex = 0;
      for (Match match in keywordPattern.allMatches(code)) {
        // Add text before the keyword
        if (match.start > lastIndex) {
          String beforeText = code.substring(lastIndex, match.start);
          spans.addAll(
              _processStringSegment(beforeText, stringPattern, stringColor));
        }
        // Add the highlighted keyword
        spans.add(TextSpan(
          text: match.group(0),
          style: TextStyle(color: keywordColor, fontWeight: FontWeight.bold),
        ));
        lastIndex = match.end;
      }
      // Add any remaining text
      if (lastIndex < code.length) {
        String afterText = code.substring(lastIndex);
        spans.addAll(
            _processStringSegment(afterText, stringPattern, stringColor));
      }
    } else {
      spans.addAll(_processStringSegment(code, stringPattern, stringColor));
    }

    return spans;
  }

  // Helper method to process strings
  List<TextSpan> _processStringSegment(
      String code, RegExp stringPattern, Color stringColor) {
    List<TextSpan> spans = [];

    if (stringPattern.hasMatch(code)) {
      int lastIndex = 0;
      for (Match match in stringPattern.allMatches(code)) {
        // Add text before the string
        if (match.start > lastIndex) {
          spans.add(TextSpan(text: code.substring(lastIndex, match.start)));
        }
        // Add the highlighted string
        spans.add(TextSpan(
          text: match.group(0),
          style: TextStyle(color: stringColor),
        ));
        lastIndex = match.end;
      }
      // Add any remaining text
      if (lastIndex < code.length) {
        spans.add(TextSpan(text: code.substring(lastIndex)));
      }
    } else {
      spans.add(TextSpan(text: code));
    }

    return spans;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
     appBar:   AppBar(
       automaticallyImplyLeading: false,
        backgroundColor: Color(0xFFfef8ff),
        // foregroundColor: Colors.black,
        elevation: 0,
        title: Text('NSL Java Code Comparison',style: TextStyle(color:Colors.black)),
        actions: [
          // Theme toggle button
          // IconButton(
          //     onPressed: () {
          //       setState(() {
          //         showJavaKeywords = false;
          //         selectedSentenceId = null;
          //         _selectedKeyword = null;
          //       });
          //       // Scroll to top after state update
          //       Future.delayed(const Duration(milliseconds: 50), () {
          //         if (_codeScrollController.hasClients) {
          //           _codeScrollController.animateTo(
          //             0,
          //             duration: const Duration(milliseconds: 300),
          //             curve: Curves.easeInOut,
          //           );
          //         }
          //       });
          //     },
          //     icon: Icon(Icons.refresh)),
          IconButton(
            icon: Icon(_isDarkMode ? Icons.light_mode : Icons.dark_mode),
            tooltip:
                _isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode',
            onPressed: _toggleThemeMode,
            color: Colors.black,
          ),
        ],
      ),
      
      body: Row(
        children: [
          // Sidebar
          // const Sidebar(),
          // Main content
          Expanded(
            child: _isLoadingJavaFile
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Loading Java file from API...'),
                      ],
                    ),
                  )
                : _javaFileError != null
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.error, color: Colors.red, size: 48),
                            SizedBox(height: 16),
                            Text(
                              'Error loading Java file',
                              style: TextStyle(
                                  fontSize: 18, fontWeight: FontWeight.bold),
                            ),
                            SizedBox(height: 8),
                            Text(
                              _javaFileError!,
                              textAlign: TextAlign.center,
                              style: TextStyle(color: Colors.red),
                            ),
                            SizedBox(height: 16),
                            ElevatedButton(
                              onPressed: _loadJavaFile,
                              child: Text('Retry'),
                            ),
                          ],
                        ),
                      )
                    : _javaLines.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.info, size: 48, color: Colors.grey),
                                SizedBox(height: 16),
                                Text('No Java code available'),
                              ],
                            ),
                          )
                        : ResizableSplitView(
                            initialLeftPanelSize:
                                0.3, // 30% of the screen width
                            minLeftPanelSize:
                                0.2, // Minimum 20% of the screen width
                            maxLeftPanelSize:
                                0.6, // Maximum 60% of the screen width
                            leftPanel: Container(
                              color: Colors.grey[50],
                              child: Column(
                                children: [
                                  // Left panel header
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                    color: const Color(0xFF3C3F41),
                                    child: Row(
                                      children: [
                                        const Icon(Icons.api,
                                            color: Colors.white70, size: 16),
                                        const SizedBox(width: 8),
                                        Text(
                                          _javaFile?.fileName?.replaceAll(
                                                  '.java', '.nsl') ??
                                              'NSL Groups - ${widget.goId}',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                        const Spacer(),
                                        if (_isLoading)
                                          const SizedBox(
                                            width: 16,
                                            height: 16,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                      Colors.white70),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                  // API data content
                                  Expanded(
                                    child: _buildApiDataWidget(),
                                  ),
                                ],
                              ),
                            ),
                            rightPanel: Container(
                              height: MediaQuery.of(context).size.height,
                              decoration: BoxDecoration(
                                color:
                                    editorBackground, // Dynamic background color based on theme
                                border: Border.all(color: borderColor),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black
                                        .withAlpha(_isDarkMode ? 100 : 50),
                                    blurRadius: 8,
                                    offset: const Offset(0, 3),
                                  ),
                                ],
                              ),
                              child: Column(
                                children: [
                                  // Editor header
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                    color:
                                        editorHeaderColor, // Dynamic header color based on theme
                                    child: Row(
                                      children: [
                                        Icon(Icons.code,
                                            color: headerIconColor, size: 16),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            _javaFile?.fileName ?? 'Java Code',
                                            style: TextStyle(
                                              color: headerTextColor,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ),
                                        // if (_selectedLineNumbers.isNotEmpty)
                                        //   Container(
                                        //     padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                        //     decoration: BoxDecoration(
                                        //       color: Colors.orange[100],
                                        //       borderRadius: BorderRadius.circular(4),
                                        //       border: Border.all(color: Colors.orange),
                                        //     ),
                                        //     child: Text(
                                        //       'Lines: ${_selectedLineNumbers.join(", ")}',
                                        //       style: TextStyle(
                                        //         color: Colors.orange[800],
                                        //         fontSize: 12,
                                        //         fontWeight: FontWeight.bold,
                                        //       ),
                                        //     ),
                                        //   ),
                                      ],
                                    ),
                                  ),
                                  // Code content with both horizontal and vertical scrolling
                                  Expanded(
                                    child: ScrollConfiguration(
                                      behavior: ScrollConfiguration.of(context)
                                          .copyWith(
                                        scrollbars: true,
                                        dragDevices: {
                                          PointerDeviceKind.mouse,
                                          PointerDeviceKind.touch,
                                          PointerDeviceKind.trackpad,
                                        },
                                      ),
                                      child: SingleChildScrollView(
                                        key: _codeViewKey,
                                        controller: _codeScrollController,
                                        scrollDirection: Axis.vertical,
                                        child: SingleChildScrollView(
                                          scrollDirection: Axis.horizontal,
                                          child: Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: _buildCodeLines(),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
          ),
        ],
      ),
    );
  }
}

/// A widget that creates a resizable split view with a draggable divider.
class ResizableSplitView extends StatefulWidget {
  final Widget leftPanel;
  final Widget rightPanel;
  final double initialLeftPanelSize;
  final double minLeftPanelSize;
  final double maxLeftPanelSize;

  const ResizableSplitView({
    super.key,
    required this.leftPanel,
    required this.rightPanel,
    this.initialLeftPanelSize = 0.3,
    this.minLeftPanelSize = 0.1,
    this.maxLeftPanelSize = 0.7,
  });

  @override
  State<ResizableSplitView> createState() => _ResizableSplitViewState();
}

class _ResizableSplitViewState extends State<ResizableSplitView>
    with SingleTickerProviderStateMixin {
  late double _leftPanelWidth;
  final double _dividerWidth = 6.0;
  bool _isDragging = false;

  // Animation controller for hover effect
  late AnimationController _hoverAnimationController;
  bool _isHovering = false;

  @override
  void initState() {
    super.initState();
    _leftPanelWidth = widget.initialLeftPanelSize;

    // Initialize animation controller for hover effect
    _hoverAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
  }

  @override
  void dispose() {
    _hoverAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final leftPanelPixelWidth = _leftPanelWidth * width;

        return Stack(
          children: [
            // Left panel
            Positioned(
              left: 0,
              top: 0,
              bottom: 0,
              width: leftPanelPixelWidth,
              child: widget.leftPanel,
            ),

            // Divider
            Positioned(
              left: leftPanelPixelWidth - _dividerWidth / 2,
              top: 0,
              bottom: 0,
              width: _dividerWidth,
              child: MouseRegion(
                cursor: SystemMouseCursors.resizeLeftRight,
                onEnter: (_) {
                  setState(() {
                    _isHovering = true;
                  });
                  _hoverAnimationController.forward();
                },
                onExit: (_) {
                  setState(() {
                    _isHovering = false;
                  });
                  if (!_isDragging) {
                    _hoverAnimationController.reverse();
                  }
                },
                child: GestureDetector(
                  onHorizontalDragStart: (_) {
                    setState(() {
                      _isDragging = true;
                    });
                    _hoverAnimationController.forward();
                  },
                  onHorizontalDragUpdate: (details) {
                    setState(() {
                      // Calculate new width as a percentage of total width
                      final newLeftPanelWidth =
                          (_leftPanelWidth * width + details.delta.dx) / width;

                      // Constrain to min/max values
                      _leftPanelWidth = newLeftPanelWidth.clamp(
                        widget.minLeftPanelSize,
                        widget.maxLeftPanelSize,
                      );
                    });
                  },
                  onHorizontalDragEnd: (_) {
                    setState(() {
                      _isDragging = false;
                    });
                    if (!_isHovering) {
                      _hoverAnimationController.reverse();
                    }
                  },
                  child: AnimatedBuilder(
                    animation: _hoverAnimationController,
                    builder: (context, child) {
                      final double animValue = _hoverAnimationController.value;

                      return Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            colors: [
                              Color.lerp(Colors.grey[300], Colors.blue[300],
                                  animValue)!,
                              Color.lerp(Colors.grey[400], Colors.blue[400],
                                  animValue)!,
                            ],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Color.lerp(Colors.black.withAlpha(20),
                                  Colors.blue.withAlpha(60), animValue)!,
                              blurRadius: 3 + animValue * 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: LayoutBuilder(
                          builder: (context, constraints) {
                            return Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  // Top handle
                                  Container(
                                    height: constraints.maxHeight * 0.1,
                                    width: 4,
                                    decoration: BoxDecoration(
                                      color: Color.lerp(Colors.grey[200],
                                          Colors.white, animValue),
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                  SizedBox(height: 4 + animValue * 2),
                                  // Middle handle - larger
                                  Container(
                                    height: constraints.maxHeight *
                                        (0.2 + animValue * 0.05),
                                    width: 4,
                                    decoration: BoxDecoration(
                                      color: Color.lerp(Colors.grey[200],
                                          Colors.white, animValue),
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                  SizedBox(height: 4 + animValue * 2),
                                  // Bottom handle
                                  Container(
                                    height: constraints.maxHeight * 0.1,
                                    width: 4,
                                    decoration: BoxDecoration(
                                      color: Color.lerp(Colors.grey[200],
                                          Colors.white, animValue),
                                      borderRadius: BorderRadius.circular(2),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),

            // Right panel
            Positioned(
              left: leftPanelPixelWidth + _dividerWidth / 2,
              top: 0,
              bottom: 0,
              right: 0,
              child: widget.rightPanel,
            ),
          ],
        );
      },
    );
  }
}
